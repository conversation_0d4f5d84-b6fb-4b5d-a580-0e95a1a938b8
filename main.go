package main

import (
	"bytes"
	"context"
	"encoding/binary"
	"flag"
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"

	"github.com/cilium/ebpf/link"
	"github.com/cilium/ebpf/ringbuf"
	"github.com/cilium/ebpf/rlimit"
)

//go:generate go run github.com/cilium/ebpf/cmd/bpf2go -cc clang -cflags "-O2 -g -Wall -Werror" -no-global-types schedlat bpf/schedlat.c

type SchedEvent struct {
	Pid        uint32
	Tgid       uint32
	Comm       [16]byte
	WakeupTime uint64
	SwitchTime uint64
	LatencyNs  uint64
}

func main() {
	// 解析命令行参数
	var targetPID = flag.Int("p", 0, "监控特定PID的调度延迟 (0表示监控所有进程)")
	flag.Parse()

	// 移除内存限制
	if err := rlimit.RemoveMemlock(); err != nil {
		log.Fatal(err)
	}

	// 加载预编译的程序和映射到内核中
	spec, err := loadSchedlat()
	if err != nil {
		log.Fatal(err)
	}

	objs := schedlatObjects{}
	if err := spec.LoadAndAssign(&objs, nil); err != nil {
		log.Fatal(err)
	}
	defer objs.Close()

	// 设置目标PID到BPF映射中
	configKey := uint32(0)
	targetPIDValue := uint32(*targetPID)
	if err := objs.Config.Put(configKey, targetPIDValue); err != nil {
		log.Fatalf("设置目标PID失败: %v", err)
	}

	// 附加到 sched_wakeup tracepoint
	wakeupLink, err := link.Tracepoint("sched", "sched_wakeup", objs.HandleSchedWakeup, nil)
	if err != nil {
		log.Fatal(err)
	}
	defer wakeupLink.Close()

	// 附加到 sched_switch tracepoint
	switchLink, err := link.Tracepoint("sched", "sched_switch", objs.HandleSchedSwitch, nil)
	if err != nil {
		log.Fatal(err)
	}
	defer switchLink.Close()

	if *targetPID == 0 {
		fmt.Println("监控所有进程的调度延迟... 按 Ctrl+C 退出")
	} else {
		fmt.Printf("监控PID %d 的调度延迟... 按 Ctrl+C 退出\n", *targetPID)
	}
	fmt.Printf("%-8s %-16s %-12s\n", "PID", "COMM", "LATENCY(us)")

	// 打开 ringbuf reader
	rd, err := ringbuf.NewReader(objs.Events)
	if err != nil {
		log.Fatal(err)
	}
	defer rd.Close()

	// 设置信号处理
	ctx, cancel := context.WithCancel(context.Background())
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)
	go func() {
		<-c
		cancel()
	}()

	// 读取事件
	go func() {
		for {
			record, err := rd.Read()
			if err != nil {
				if ctx.Err() != nil {
					return
				}
				log.Printf("读取 ringbuf 失败: %v", err)
				continue
			}

			var event SchedEvent
			if err := binary.Read(bytes.NewBuffer(record.RawSample), binary.LittleEndian, &event); err != nil {
				log.Printf("解析事件失败: %v", err)
				continue
			}

			comm := string(event.Comm[:bytes.IndexByte(event.Comm[:], 0)])
			latencyUs := float64(event.LatencyNs) / 1000.0

			fmt.Printf("%-8d %-16s %-12.3f\n",
				event.Pid, comm, latencyUs)
		}
	}()

	<-ctx.Done()
	fmt.Println("\n程序退出")
}
