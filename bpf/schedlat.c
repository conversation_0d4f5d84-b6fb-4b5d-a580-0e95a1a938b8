typedef _Bool bool;

typedef __int128 unsigned __u128;

typedef __u128 u128;

typedef u128 freelist_full_t;

typedef const char (* const ethnl_string_array_t)[32];

typedef int __kernel_clockid_t;

typedef int __kernel_daddr_t;

typedef int __kernel_pid_t;

typedef int __kernel_rwf_t;

typedef int __kernel_timer_t;

typedef int __s32;

typedef int class_get_unused_fd_t;

typedef __kernel_clockid_t clockid_t;

typedef __s32 s32;

typedef s32 compat_int_t;

typedef s32 compat_ssize_t;

typedef int folio_walk_flags_t;

typedef int fpb_t;

typedef int fpi_t;

typedef int initcall_entry_t;

typedef s32 int32_t;

typedef s32 old_time32_t;

typedef int pci_power_t;

typedef __kernel_pid_t pid_t;

typedef int rmap_t;

typedef __kernel_rwf_t rwf_t;

typedef int suspend_state_t;

typedef const int tracepoint_ptr_t;

typedef long int __kernel_long_t;

typedef __kernel_long_t __kernel_clock_t;

typedef __kernel_long_t __kernel_off_t;

typedef __kernel_long_t __kernel_old_time_t;

typedef __kernel_long_t __kernel_ptrdiff_t;

typedef __kernel_long_t __kernel_ssize_t;

typedef __kernel_long_t __kernel_suseconds_t;

typedef __kernel_clock_t clock_t;

typedef __kernel_off_t off_t;

typedef volatile long int prel64_t;

typedef __kernel_ptrdiff_t ptrdiff_t;

typedef __kernel_ssize_t ssize_t;

typedef __kernel_suseconds_t suseconds_t;

typedef long long int __s64;

typedef __s64 Elf64_Sxword;

typedef long long int __kernel_loff_t;

typedef long long int __kernel_time64_t;

typedef __s64 s64;

typedef s64 int64_t;

typedef s64 ktime_t;

typedef __kernel_loff_t loff_t;

typedef long long int qsize_t;

typedef __s64 time64_t;

typedef long long unsigned int __u64;

typedef __u64 Elf64_Addr;

typedef __u64 Elf64_Off;

typedef __u64 Elf64_Xword;

typedef __u64 __addrpair;

typedef __u64 __be64;

typedef __u64 __le64;

typedef __u64 u64;

typedef u64 async_cookie_t;

typedef u64 blkcnt_t;

typedef u64 dma_addr_t;

typedef __be64 fdt64_t;

typedef u64 io_req_flags_t;

typedef u64 netdev_features_t;

typedef u64 p4dval_t;

typedef u64 pgdval_t;

typedef u64 phys_addr_t;

typedef u64 pmdval_t;

typedef u64 pteval_t;

typedef u64 pudval_t;

typedef phys_addr_t resource_size_t;

typedef u64 sci_t;

typedef u64 sector_t;

typedef __u64 timeu64_t;

typedef u64 uint64_t;

typedef long unsigned int __kernel_ulong_t;

typedef __kernel_ulong_t __kernel_size_t;

typedef long unsigned int cycles_t;

typedef __kernel_ulong_t ino_t;

typedef long unsigned int irq_hw_number_t;

typedef long unsigned int kernel_ulong_t;

typedef long unsigned int netmem_ref;

typedef long unsigned int perf_trace_t[1024];

typedef long unsigned int pte_marker;

typedef __kernel_size_t size_t;

typedef long unsigned int uintptr_t;

typedef long unsigned int ulong;

typedef long unsigned int vm_flags_t;

typedef short int __s16;

typedef __s16 s16;

typedef short unsigned int __u16;

typedef __u16 Elf32_Half;

typedef __u16 Elf64_Half;

typedef __u16 __be16;

typedef short unsigned int __kernel_sa_family_t;

typedef __u16 __le16;

typedef __u16 __sum16;

typedef short unsigned int pci_bus_flags_t;

typedef short unsigned int pci_dev_flags_t;

typedef __kernel_sa_family_t sa_family_t;

typedef __u16 u16;

typedef u16 uint16_t;

typedef short unsigned int umode_t;

typedef signed char __s8;

typedef __s8 s8;

typedef unsigned char __u8;

typedef __u8 u8;

typedef u8 blk_status_t;

typedef unsigned char cc_t;

typedef u8 dscp_t;

typedef u8 u_int8_t;

typedef u8 uint8_t;

typedef unsigned int __u32;

typedef __u32 Elf32_Addr;

typedef __u32 Elf32_Off;

typedef __u32 Elf32_Word;

typedef __u32 Elf64_Word;

typedef __u32 __be32;

typedef __u32 u32;

typedef u32 __kernel_dev_t;

typedef unsigned int __kernel_gid32_t;

typedef unsigned int __kernel_uid32_t;

typedef __u32 __le32;

typedef unsigned int __poll_t;

typedef __u32 __portpair;

typedef __u32 __wsum;

typedef unsigned int blk_features_t;

typedef unsigned int blk_flags_t;

typedef unsigned int blk_mode_t;

typedef __u32 blk_opf_t;

typedef unsigned int blk_qc_t;

typedef u32 compat_caddr_t;

typedef u32 compat_size_t;

typedef u32 compat_uint_t;

typedef u32 compat_ulong_t;

typedef u32 compat_uptr_t;

typedef u32 depot_stack_handle_t;

typedef __kernel_dev_t dev_t;

typedef u32 errseq_t;

typedef __be32 fdt32_t;

typedef unsigned int fgf_t;

typedef unsigned int fmode_t;

typedef unsigned int fop_flags_t;

typedef unsigned int gfp_t;

typedef __kernel_gid32_t gid_t;

typedef unsigned int iov_iter_extraction_t;

typedef unsigned int kasan_vmalloc_flags_t;

typedef __le32 kprobe_opcode_t;

typedef unsigned int pci_channel_state_t;

typedef unsigned int pci_ers_result_t;

typedef unsigned int pgtbl_mod_mask;

typedef u32 phandle;

typedef unsigned int pipe_index_t;

typedef __kernel_uid32_t projid_t;

typedef unsigned int sk_buff_data_t;

typedef unsigned int slab_flags_t;

typedef unsigned int speed_t;

typedef unsigned int t_key;

typedef unsigned int tcflag_t;

typedef __kernel_uid32_t uid_t;

typedef unsigned int uint;

typedef u32 uint32_t;

typedef __le32 uprobe_opcode_t;

typedef unsigned int vm_fault_t;

typedef unsigned int xa_mark_t;

typedef u32 xdp_features_t;

typedef unsigned int zap_flags_t;

#include <bpf/bpf_helpers.h>
#include <bpf/bpf_tracing.h>

#define BPF_MAP_TYPE_RINGBUF 27
#define BPF_MAP_TYPE_HASH 1
#define BPF_ANY 0

struct trace_entry {
	unsigned short type;
	unsigned char flags;
	unsigned char preempt_count;
	int pid;
	unsigned char preempt_lazy_count;
};

struct trace_event_raw_sched_wakeup_template {
	struct trace_entry ent;
	char comm[16];
	pid_t pid;
	int prio;
	int target_cpu;
	char __data[0];
};

struct trace_event_raw_sched_switch {
	struct trace_entry ent;
	char prev_comm[16];       
	pid_t prev_pid;          
	int prev_prio;           
	long prev_state;         
	char next_comm[16];      
	pid_t next_pid;          
	int next_prio;           
	char __data[0];
};

struct sched_event {
    u32 pid;
    u32 tgid;
    char comm[16];
    u64 wakeup_time;
    u64 switch_time;
    u64 latency_ns;
};

struct {
    __uint(type, BPF_MAP_TYPE_RINGBUF);
    __uint(max_entries, 256 * 1024);
} events SEC(".maps");

struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __uint(max_entries, 10240);
    __type(key, u32);
    __type(value, u64);
} wakeup_times SEC(".maps");

// 配置映射，用于存储目标PID (0表示监控所有进程)
struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __uint(max_entries, 1);
    __type(key, u32);
    __type(value, u32);
} config SEC(".maps");

SEC("tracepoint/sched/sched_wakeup")
int handle_sched_wakeup(struct trace_event_raw_sched_wakeup_template *ctx)
{
    //直接通过结构体获取成员
    u32 pid = ctx->pid;
    u64 ts = bpf_ktime_get_ns();

    //过滤掉idle进程
    if(pid == 0){
        return 0;
    }

    // 检查是否需要过滤特定进程组
    u32 config_key = 0;
    u32 *target_tgid = bpf_map_lookup_elem(&config, &config_key);
    if (target_tgid && *target_tgid != 0) {
        // 获取当前任务的TGID
        u64 pid_tgid = bpf_get_current_pid_tgid();
        u32 tgid = pid_tgid >> 32;

        if (*target_tgid != tgid) {
            return 0; // 如果设置了目标TGID且当前TGID不匹配，则跳过
        }
    }

    bpf_map_update_elem(&wakeup_times, &pid, &ts, BPF_ANY);

    return 0;
}

SEC("tracepoint/sched/sched_switch")
int handle_sched_switch(struct trace_event_raw_sched_switch *ctx)
{
    u32 next_pid = ctx->next_pid;
    u64 now = bpf_ktime_get_ns();

    // 检查是否需要过滤特定进程组
    u32 config_key = 0;
    u32 *target_tgid = bpf_map_lookup_elem(&config, &config_key);
    if (target_tgid && *target_tgid != 0) {
        // 获取当前任务的TGID
        u64 pid_tgid = bpf_get_current_pid_tgid();
        u32 tgid = pid_tgid >> 32;

        if (*target_tgid != tgid) {
            return 0; // 如果设置了目标TGID且当前TGID不匹配，则跳过
        }
    }

    // 查找被调度进程的上次唤醒时间
    u64 *wakeup_ts = bpf_map_lookup_elem(&wakeup_times, &next_pid);
    if (!wakeup_ts) {
        return 0;
    }
    
    u64 latency = now - *wakeup_ts;
    
    // // 过滤掉太小的延迟
    // if (latency < 1000) { // 小于1微秒
    //     bpf_map_delete_elem(&wakeup_times, &next_pid);
    //     return 0;
    // }
    
    struct sched_event *e;
    e = bpf_ringbuf_reserve(&events, sizeof(*e), 0);
    if (!e) {
        return 0;
    }
    
    e->pid = next_pid;
    // 获取真实的TGID
    u64 pid_tgid = bpf_get_current_pid_tgid();
    e->tgid = pid_tgid >> 32;

    // 使用 __builtin_memcpy 进行高效复制
    __builtin_memcpy(e->comm, ctx->next_comm, sizeof(e->comm));
    e->wakeup_time = *wakeup_ts;
    e->switch_time = now;
    e->latency_ns = latency;
    
    //bpf_printk("submit: %d, %s, %lld, %lld, %lld\n", e->pid, e->comm, e->wakeup_time, e->switch_time, e->latency_ns);

    bpf_ringbuf_submit(e, 0);
    
    // 清理映射条目
    bpf_map_delete_elem(&wakeup_times, &next_pid);
    
    return 0;
}

char LICENSE[] SEC("license") = "GPL";